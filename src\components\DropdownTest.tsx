'use client'

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuCheckboxItem,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuShortcut,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
} from './ui/Dropdown'

export function DropdownTest() {
  return (
    <div className="p-8 space-y-4">
      <h2 className="text-2xl font-bold">Dropdown Component Test</h2>
      
      {/* Basic Dropdown */}
      <div>
        <h3 className="text-lg font-semibold mb-2">Basic Dropdown</h3>
        <DropdownMenu>
          <DropdownMenuTrigger className="btn btn-primary">
            Open Menu
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuLabel>My Account</DropdownMenuLabel>
            <DropdownMenuItem>
              <span className="icon-[solar--user-bold-duotone]"></span>
              Profile
              <DropdownMenuShortcut>⇧⌘P</DropdownMenuShortcut>
            </DropdownMenuItem>
            <DropdownMenuItem>
              <span className="icon-[solar--settings-bold-duotone]"></span>
              Settings
              <DropdownMenuShortcut>⌘,</DropdownMenuShortcut>
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem variant="destructive">
              <span className="icon-[solar--logout-bold-duotone]"></span>
              Log out
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Dropdown with Checkboxes */}
      <div>
        <h3 className="text-lg font-semibold mb-2">Dropdown with Checkboxes</h3>
        <DropdownMenu>
          <DropdownMenuTrigger className="btn btn-secondary">
            View Options
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuLabel>View Settings</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuCheckboxItem checked>
              Show Toolbar
            </DropdownMenuCheckboxItem>
            <DropdownMenuCheckboxItem>
              Show Sidebar
            </DropdownMenuCheckboxItem>
            <DropdownMenuCheckboxItem checked>
              Show Status Bar
            </DropdownMenuCheckboxItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Dropdown with Radio Items */}
      <div>
        <h3 className="text-lg font-semibold mb-2">Dropdown with Radio Items</h3>
        <DropdownMenu>
          <DropdownMenuTrigger className="btn btn-accent">
            Select Theme
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuLabel>Theme</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuRadioGroup value="light">
              <DropdownMenuRadioItem value="light">
                Light
              </DropdownMenuRadioItem>
              <DropdownMenuRadioItem value="dark">
                Dark
              </DropdownMenuRadioItem>
              <DropdownMenuRadioItem value="system">
                System
              </DropdownMenuRadioItem>
            </DropdownMenuRadioGroup>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Dropdown with Submenu */}
      <div>
        <h3 className="text-lg font-semibold mb-2">Dropdown with Submenu</h3>
        <DropdownMenu>
          <DropdownMenuTrigger className="btn btn-outline">
            More Actions
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuItem>
              <span className="icon-[solar--copy-bold-duotone]"></span>
              Copy
            </DropdownMenuItem>
            <DropdownMenuItem>
              <span className="icon-[solar--paste-bold-duotone]"></span>
              Paste
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuSub>
              <DropdownMenuSubTrigger>
                <span className="icon-[solar--share-bold-duotone]"></span>
                Share
              </DropdownMenuSubTrigger>
              <DropdownMenuSubContent>
                <DropdownMenuItem>
                  <span className="icon-[solar--link-bold-duotone]"></span>
                  Copy Link
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <span className="icon-[solar--letter-bold-duotone]"></span>
                  Email
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <span className="icon-[solar--chat-round-bold-duotone]"></span>
                  Message
                </DropdownMenuItem>
              </DropdownMenuSubContent>
            </DropdownMenuSub>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  )
}
